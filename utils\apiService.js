// API服务模块 - 统一入口
const provinceService = require('./provinceService');
const cityService = require('./cityService');
const scenicService = require('./scenicService');
const carouselService = require('./carouselService');
const lecturerService = require('./lecturerService');
const guidePointService = require('./guidePointService');
const relationService = require('./relationService');

class ApiService {
  constructor() {
    this.provinceService = provinceService;
    this.cityService = cityService;
    this.scenicService = scenicService;
    this.carouselService = carouselService;
    this.lecturerService = lecturerService;
    this.guidePointService = guidePointService;
    this.relationService = relationService;
  }

  // 省份相关API
  async getProvinces() {
    return this.provinceService.getProvinces();
  }

  async getProvinceById(provinceId) {
    return this.provinceService.getProvinceById(provinceId);
  }

  // 城市相关API
  async getCities(provinceId) {
    return this.cityService.getCitiesByProvince(provinceId);
  }

  async getCityById(cityId) {
    return this.cityService.getCityById(cityId);
  }

  // 景区相关API
  async getRecommendScenics(params) {
    return this.scenicService.getRecommendScenics(params);
  }

  async getScenicsByProvince(provinceId, params) {
    return this.scenicService.getScenicsByProvince(provinceId, params);
  }

  async getScenicsByCity(cityId, params) {
    return this.scenicService.getScenicsByCity(cityId, params);
  }

  async getScenicDetail(scenicId) {
    return this.scenicService.getScenicDetail(scenicId);
  }

  async searchScenics(keyword, params) {
    return this.scenicService.searchScenics(keyword, params);
  }

  async searchScenicsByPage(params) {
    return this.scenicService.searchScenicsByPage(params);
  }

  async getCommentaryProducts(scenicId, params) {
    return this.scenicService.getCommentaryProducts(scenicId, params);
  }

  async getGuideProductDetail(productId) {
    return this.scenicService.getGuideProductDetail(productId);
  }

  async getProductReviews(productId, params) {
    return this.scenicService.getProductReviews(productId, params);
  }

  // 轮播图相关API
  async getCarousels(provinceId, type) {
    return this.carouselService.getCarousels(provinceId, type);
  }

  async getHomeCarousels(provinceId) {
    return this.carouselService.getHomeCarousels(provinceId);
  }

  async getCarouselsByProvince(provinceId, type) {
    return this.carouselService.getCarouselsByProvince(provinceId, type);
  }

  async getShouyeCarousels() {
    return this.carouselService.getShouyeCarousels();
  }

  // 讲师相关API
  async getLecturerDetail(lecturerId) {
    return this.lecturerService.getLecturerDetail(lecturerId);
  }

  async getLecturerList(params) {
    return this.lecturerService.getLecturerList(params);
  }

  // 指南点相关API
  async getGuidePointList(params) {
    return this.guidePointService.getGuidePointList(params);
  }

  async getGuidePointDetail(pointId) {
    return this.guidePointService.getGuidePointDetail(pointId);
  }

  // 关系服务相关API
  async getProductAreaDetails(productId) {
    return this.relationService.getProductAreaDetails(productId);
  }

  async getAreaPointDetails(areaId) {
    return this.relationService.getAreaPointDetails(areaId);
  }

  async getPointAudioDetails(pointId) {
    return this.relationService.getPointAudioDetails(pointId);
  }

  async getCompleteHierarchyData(productId) {
    return this.relationService.getCompleteHierarchyData(productId);
  }

  async getAreaAudioData(areaId) {
    return this.relationService.getAreaAudioData(areaId);
  }

  // 批量请求
  async batchRequest(requests) {
    try {
      const results = await Promise.allSettled(requests);
      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return { success: true, data: result.value, index };
        } else {
          console.error(`批量请求第${index + 1}个失败:`, result.reason);
          return { success: false, error: result.reason, index };
        }
      });
    } catch (error) {
      console.error('批量请求失败:', error);
      throw error;
    }
  }

  // 错误处理
  handleError(error, showToast = true) {
    console.error('API请求错误:', error);

    if (showToast) {
      wx.showToast({
        title: error.message || '请求失败',
        icon: 'none',
        duration: 2000
      });
    }

    return error;
  }

  // 清除所有缓存
  clearAllCache() {
    this.provinceService.clearCache();
    this.cityService.clearCache();
    this.scenicService.clearCache();
    this.carouselService.clearCache();
    this.lecturerService.clearCache();
    this.guidePointService.clearCache();
    this.relationService.clearCache();
    console.log('所有缓存已清除');
  }
}

// 创建单例实例
const apiService = new ApiService();

module.exports = apiService;
