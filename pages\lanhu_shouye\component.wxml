<view class="page">
  <view class="group_1"></view>
  <view class="group_2">
    <view class="section_1">
      <view class="image-text_1">
        <image src="/images/dingwei.png" class="section_2"></image>
        <text lines="1" class="{{provinces[0].selected ? 'text-group_1' : ''}}" bindtap="selectProvince" data-index="0">{{provinces[0].name}}省</text>
      </view>
      <!-- 显示前4个未选中的省份 -->
      <block wx:for="{{provinces}}" wx:key="name" wx:if="{{index > 0 && index <= 4}}">
        <text lines="1" class="text_{{index}}" bindtap="selectProvince" data-index="{{index}}">{{item.name}}</text>
      </block>
      <image src="/images/xiasanjiao.png" class="box_1" bindtap="toggleProvinceModal"></image>
    </view>

    <!-- 省份选择弹窗 -->
    <view wx:if="{{showProvinceModal}}" class="province-modal-overlay" bindtap="closeProvinceModal">
      <view class="province-modal" catchtap="stopPropagation">
        <view class="modal-header">
          <text class="modal-title">选择省份</text>
          <text class="modal-close" bindtap="closeProvinceModal">×</text>
        </view>
        <view class="modal-content">
          <view class="province-grid">
            <block wx:for="{{provinces}}" wx:key="name">
              <view class="province-item {{item.selected ? 'selected' : ''}}" bindtap="selectProvinceFromModal" data-index="{{index}}">
                <text class="province-text">{{item.name}}</text>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <view class="section_3"></view>

    <!-- 轮播图容器 -->
    <view class="carousel-container">
      <swiper class="carousel-swiper" indicator-dots="{{currentCarouselData.length > 1}}" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="rgba(255,255,255,0.9)" autoplay="{{carouselAutoPlay && currentCarouselData.length > 1}}" interval="{{carouselInterval}}" duration="500" circular="{{currentCarouselData.length > 1}}" current="{{currentCarouselIndex}}" bindchange="onCarouselChange" bindtouchstart="onCarouselTouchStart" bindtouchend="onCarouselTouchEnd">

        <block wx:for="{{currentCarouselData}}" wx:key="id">
          <swiper-item>
            <view class="carousel-item" bindtap="onCarouselImageTap" data-index="{{index}}">
              <image src="{{item.image}}" class="image_1 carousel-image" mode="aspectFill">
              </image>
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>
    <view class="section_4">
      <view class="image-text_2">
        <image src="../../images/lanhu_shouye/45c507e649d74ac891936f830b3e934d_mergeImage.png" class="thumbnail_1"></image>
        <text lines="1" class="text-group_2">资讯</text>
      </view>
      <view class="section_5"></view>
      <text lines="1" class="text_5">{{currentWelcomeMessage}}</text>
      <view class="image-text_3" bindtap="onMoreButtonTap">
        <text lines="1" class="text-group_3">更多</text>
        <image src="../../images/lanhu_shouye/FigmaDDSSlicePNGad1ba875626993d892b3784b64e8205e.png" class="thumbnail_2"></image>
      </view>
    </view>
  </view>
  <!-- 精选推荐板块  -->
  <view class="group_3">
    <view class="box_2">
      <view class="group_4">
        <view class="box_3">
          <image src="../../images/lanhu_shouye/FigmaDDSSlicePNG6fb1635187c60885e6abfd33425d8aaf.png" class="image_2"></image>
          <text lines="1" class="text_6">精选推荐</text>
        </view>
      </view>

      <!-- 城市选择器 - 只在有城市数据时显示 -->
      <scroll-view class="group_5" scroll-x="true" enable-flex="true" wx:if="{{cityList && cityList.length > 0}}">
        <view wx:for="{{cityList}}" wx:key="id" class="city-item {{selectedCityId === item.id ? 'active' : ''}}" bindtap="selectCity" data-city-id="{{item.id}}" data-city-name="{{item.name}}">
          <text lines="1" class="city-text">{{item.name}}</text>
        </view>
      </scroll-view>

      <!-- 动态景区卡片列表 -->
      <view class="scenic-cards-container" wx:if="{{currentScenicList && currentScenicList.length > 0}}">
        <block wx:for="{{currentScenicList}}" wx:key="id" >
          <view class="scenic-card-item" bindtap="onScenicCardTap" data-scenic-id="{{item.scenic_id || item.scenicId}}">

            <!-- 景区图片 -->
            <image src="{{item.image}}" class="scenic-card-image" mode="aspectFill" show-menu-by-longpress="false" binderror="onImageError" bindload="onImageLoad">
            </image>

            <!-- 景区信息 -->
            <view class="scenic-card-content">
              <view class="scenic-card-header">
                <text lines="1" class="scenic-card-title">{{item.title}}</text>
                <text lines="2" class="scenic-card-subtitle">{{item.subtitle}}</text>
              </view>

              <view class="scenic-card-footer">
                <view class="scenic-card-description">
                  <text lines="1" class="scenic-description-text">{{item.description}}</text>
                </view>
                <view class="scenic-card-button">
                  <text lines="1" class="scenic-button-text">立即购买</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 顶部导航栏样式 -->
  <view class="group_8">
    <view class="block_1" bindtap="onBackgroundImageTap">
      <image src="{{backgroundImage}}" class="block_1" mode="aspectFill"></image>
      <view class="box_6">
        <view class="box_7">
          <view class="location-container">
            <text wx:if="{{isLoading}}" lines="1" class="text_23">定位中...</text>
            <text wx:elif="{{locationFailed}}" lines="1" class="text_23" bindtap="retryGetLocation">定位失败</text>
            <text wx:else lines="1" class="text_23">{{currentAddress}}</text>
          </view>
          <view class="image-text_6">
            <text lines="1" class="text-group_8">{{weather.temp}}</text>
          </view>
        </view>
        <view class="box_8">
          <image src="/images/Vector.png" class="box_9"></image>
          <view lines="1" class="text_24" bind:tap="goToSearch">搜索景区/博物馆/讲师</view>
        </view>
      </view>
    </view>
  </view>


</view>