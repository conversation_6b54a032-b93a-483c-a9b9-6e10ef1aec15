const apiService = require('../../utils/apiService');

Page({
  data: {
    // 页面参数
    scenicId: '',
    productId: '',

    // 景区详情数据
    scenicDetail: null,
    productDetail: null,

    // 景区信息（用于显示）
    scenicInfo: {
      title: '',
      description: '',
      address: '',
      price: 0,
      image: ''
    },

    // 购买相关
    quantity: 1,
    maxQuantity: 99,
    totalAmount: 0,

    // 用户信息
    userInfo: null,
    userId: null,

    // 页面状态
    loading: true,
    loadingText: '加载中...',
    error: false,
    errorMessage: '',
    processing: false
  },

  onLoad: function(options) {
    console.log('订单确认页面加载');
    console.log('接收到的页面参数:', options);

    // 解析页面参数
    this.setData({
      scenicId: options.scenicId || '',
      productId: options.productId || ''
    });

    // 初始化页面数据
    this.initPageData();
  },

  // 初始化页面数据
  initPageData: async function() {
    try {
      this.setData({
        loading: true,
        loadingText: '加载订单信息...',
        error: false
      });

      // 检查用户登录状态
      const loginStatus = await apiService.checkLoginStatus();
      if (!loginStatus.isLoggedIn) {
        // 用户未登录，跳转到登录页面
        wx.showModal({
          title: '提示',
          content: '请先登录后再购买',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({
                url: '/pages/lanhu_wode/component'
              });
            } else {
              wx.navigateBack();
            }
          }
        });
        return;
      }

      // 设置用户信息
      this.setData({
        userInfo: loginStatus.userInfo,
        userId: loginStatus.userInfo.id || loginStatus.userInfo.userId
      });

      // 检查必要参数
      if (!this.data.scenicId) {
        throw new Error('景区ID不能为空');
      }

      // 加载景区详情
      await this.loadScenicDetail();

      // 如果有产品ID，加载产品详情
      if (this.data.productId) {
        await this.loadProductDetail();
      }

      console.log('订单信息初始化完成');

    } catch (error) {
      console.error('初始化页面数据失败:', error);
      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '加载失败，请重试'
      });
    }
  },

  // 加载景区详情
  loadScenicDetail: async function() {
    try {
      console.log('加载景区详情:', this.data.scenicId);

      this.setData({
        loadingText: '加载景区信息...'
      });

      const scenicDetail = await apiService.getScenicDetail(this.data.scenicId);
      console.log('景区详情加载成功:', scenicDetail);

      // 映射景区详情到显示信息
      const scenicInfo = {
        title: scenicDetail.title || scenicDetail.name || '未知景区',
        description: scenicDetail.description || scenicDetail.subtitle || '',
        address: scenicDetail.address || '地址信息暂无',
        price: parseFloat(scenicDetail.price || 0),
        image: scenicDetail.image || scenicDetail.images?.[0] || ''
      };

      // 计算总金额
      const totalAmount = scenicInfo.price * this.data.quantity;

      this.setData({
        scenicDetail: scenicDetail,
        scenicInfo: scenicInfo,
        totalAmount: totalAmount,
        loading: false
      });

    } catch (error) {
      console.error('加载景区详情失败:', error);
      throw new Error('加载景区信息失败：' + (error.message || '未知错误'));
    }
  },

  // 加载产品详情
  loadProductDetail: async function() {
    try {
      console.log('加载产品详情:', this.data.productId);

      this.setData({
        loadingText: '加载产品信息...'
      });

      const productDetail = await apiService.getGuideProductDetail(this.data.productId);
      console.log('产品详情加载成功:', productDetail);

      // 如果产品有价格，使用产品价格覆盖景区价格
      if (productDetail.price && productDetail.price > 0) {
        const updatedScenicInfo = {
          ...this.data.scenicInfo,
          price: parseFloat(productDetail.price),
          title: productDetail.title || this.data.scenicInfo.title,
          description: productDetail.description || this.data.scenicInfo.description
        };

        // 重新计算总金额
        const totalAmount = updatedScenicInfo.price * this.data.quantity;

        this.setData({
          productDetail: productDetail,
          scenicInfo: updatedScenicInfo,
          totalAmount: totalAmount
        });
      } else {
        this.setData({
          productDetail: productDetail
        });
      }

    } catch (error) {
      console.error('加载产品详情失败:', error);
      // 产品详情加载失败不影响主流程，只记录错误
      console.warn('产品详情加载失败，将使用景区基础信息');
    }
  },

  // 重试加载
  onRetryLoad: function() {
    this.initPageData();
  },

  // 增加数量
  onIncreaseQuantity: function() {
    if (this.data.quantity >= this.data.maxQuantity) {
      wx.showToast({
        title: `最多购买${this.data.maxQuantity}张`,
        icon: 'none'
      });
      return;
    }

    const newQuantity = this.data.quantity + 1;
    const totalAmount = this.data.scenicInfo.price * newQuantity;

    this.setData({
      quantity: newQuantity,
      totalAmount: totalAmount
    });
  },

  // 减少数量
  onDecreaseQuantity: function() {
    if (this.data.quantity <= 1) {
      return;
    }

    const newQuantity = this.data.quantity - 1;
    const totalAmount = this.data.scenicInfo.price * newQuantity;

    this.setData({
      quantity: newQuantity,
      totalAmount: totalAmount
    });
  },

  // 支付处理
  onPayment: async function() {
    if (this.data.processing) {
      return;
    }

    try {
      this.setData({
        processing: true,
        loadingText: '处理支付中...'
      });

      console.log('开始支付流程');

      // 第一步：创建订单
      const orderData = {
        userId: this.data.userId,
        totalAmount: this.data.totalAmount,
        scenicId: this.data.scenicId,
        productId: this.data.productId,
        quantity: this.data.quantity,
        productName: this.data.scenicInfo.title,
        productPrice: this.data.scenicInfo.price
      };

      console.log('创建订单:', orderData);
      const order = await apiService.createOrder(orderData);
      console.log('订单创建成功:', order);

      // 第二步：处理支付
      const paymentData = {
        amount: this.data.totalAmount,
        orderId: order.id || order.orderId,
        productName: this.data.scenicInfo.title
      };

      console.log('处理支付:', paymentData);
      const paymentResult = await apiService.processPayment(order.id || order.orderId, paymentData);
      console.log('支付处理完成:', paymentResult);

      if (!paymentResult.success) {
        throw new Error('支付失败');
      }

      // 第三步：更新订单状态为已支付
      console.log('更新订单状态为已支付');
      await apiService.updateOrderStatus(order.id || order.orderId, 'paid');

      // 第四步：创建产品
      const productData = {
        name: this.data.scenicInfo.title,
        description: this.data.scenicInfo.description,
        price: this.data.scenicInfo.price,
        scenicId: this.data.scenicId,
        productType: 'single',
        status: 1,
        requireActivation: true,
        validityHours: 24,
        image: this.data.scenicInfo.image
      };

      console.log('创建产品:', productData);
      const product = await apiService.createProduct(productData);
      console.log('产品创建成功:', product);

      // 第五步：创建门票
      const couponOrderData = {
        orderId: order.id || order.orderId,
        productId: product.id || product.productId,
        userId: this.data.userId,
        productName: this.data.scenicInfo.title,
        value: this.data.scenicInfo.price
      };

      console.log('批量创建门票:', couponOrderData, this.data.quantity);
      const coupons = await apiService.createMultipleCoupons(couponOrderData, this.data.quantity);
      console.log('门票创建成功:', coupons);

      // 支付成功，显示成功提示
      wx.showToast({
        title: '购买成功！',
        icon: 'success',
        duration: 2000
      });

      // 延迟跳转到订单详情或我的订单页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/lanhu_quanbudingdan/component?orderId=${order.id || order.orderId}`
        });
      }, 2000);

    } catch (error) {
      console.error('支付流程失败:', error);

      this.setData({
        processing: false
      });

      wx.showModal({
        title: '支付失败',
        content: error.message || '支付过程中出现错误，请重试',
        showCancel: false,
        confirmText: '确定'
      });
    }
  }
});
