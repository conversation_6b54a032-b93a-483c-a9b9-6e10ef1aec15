<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">{{loadingText}}</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <text class="error-text">{{errorMessage}}</text>
    <button bindtap="onRetryLoad" class="retry-btn">重试</button>
  </view>

  <!-- 订单确认内容 -->
  <view wx:else class="content-container">
    <view class="block_1">
      <view class="box_1">
        <image src="" class="thumbnail_3"></image>
        <text lines="1" class="text_1">填写订单信息</text>
      </view>
      <!-- 产品信息展示区域 -->
      <view class="box_2 scenic-info-container">
        <view class="scenic-info">
          <image src="{{scenicDetail.image}}" class="scenic-image" mode="aspectFill"></image>
          <view class="scenic-details">
            <text class="scenic-title">{{scenicDetail.title}}</text>
            <view class="product-extra-info">
              <text class="product-duration">时长：{{productInfo.duration}}</text>
              <text class="product-points">讲解点：{{productInfo.pointCount}}个</text>
            </view>
            <text class="scenic-address" wx:if="{{scenicInfo.address}}">地址：{{scenicInfo.address}}</text>
            <view class="scenic-price">
              <text class="price-label">单价：</text>
              <text class="price-value">￥{{productInfo.price}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="block_2">
      <view class="text-group_1">
        <text lines="1" class="text_2">购买须知</text>
        <text lines="1" decode="true" class="text_3">请仔细阅读购买须知，购买即表示同意相关条款</text>
      </view>
    </view>

    <view class="block_4">
      <view class="box_3">
        <text lines="1" class="text_10">价格</text>
        <view class="text-wrapper_3">
          <text lines="1" class="text_11">￥</text>
          <text lines="1" class="text_12">{{productInfo.price}}</text>
          <text lines="1" class="text_13">/次</text>
        </view>
      </view>
      <view class="quantity-section">
        <text lines="1" class="text_14">购买数量</text>
        <view class="quantity-controls">
          <view class="quantity-btn {{quantity <= 1 ? 'disabled' : ''}}" bindtap="onDecreaseQuantity">
            <text class="quantity-btn-text">-</text>
          </view>
          <view class="quantity-display">
            <text class="quantity-text">{{quantity}}</text>
          </view>
          <view class="quantity-btn {{quantity >= maxQuantity ? 'disabled' : ''}}" bindtap="onIncreaseQuantity">
            <text class="quantity-btn-text">+</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付区域 -->
    <view class="block_3">
      <view class="group_1">
        <view class="text-wrapper_1">
          <text lines="1" class="text_4">合计</text>
          <text lines="1" class="text_5">￥</text>
          <text lines="1" class="text_6">{{totalAmount}}</text>
          <text lines="1" class="text_7">.</text>
          <text lines="1" class="text_8">00</text>
        </view>
        <view class="text-wrapper_2 {{processing ? 'disabled' : ''}}" bindtap="onPayment">
          <text lines="1" class="text_9">{{processing ? '处理中...' : '去支付'}}</text>
        </view>
      </view>
    </view>
  </view>
</view>