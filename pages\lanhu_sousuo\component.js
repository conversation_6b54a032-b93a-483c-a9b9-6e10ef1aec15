const apiService = require('../../utils/apiService');

Component({
  properties: {},
  data: {
    searchKeyword: '',           // 搜索关键词
    showSearchResults: false,    // 是否显示搜索结果
    searchResults: {             // 搜索结果数据
      list: [],
      total: 0,
      current: 1,
      size: 20,
      pages: 1
    },
    searchHistory: [],           // 搜索历史
    recommendedSearches: [       // 推荐搜索
      '北京', '西安', '伊斯坦布尔', '成都', '重庆'
    ],
    loading: false,              // 加载状态
    error: false,                // 错误状态
    errorMessage: ''             // 错误信息
  },
  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("搜索页面加载");
      this.loadSearchHistory();
    },
    detached: function () {
      console.info("搜索页面卸载");
    },
  },
  methods: {
    // 加载搜索历史
    loadSearchHistory: function() {
      try {
        const history = wx.getStorageSync('search_history') || [];
        this.setData({
          searchHistory: history.slice(0, 5) // 最多显示5个历史记录
        });
        console.log('搜索历史加载成功:', history);
      } catch (error) {
        console.error('加载搜索历史失败:', error);
      }
    },

    // 保存搜索历史
    saveSearchHistory: function(keyword) {
      try {
        if (!keyword || keyword.trim() === '') return;

        let history = wx.getStorageSync('search_history') || [];

        // 移除重复项
        history = history.filter(item => item !== keyword);

        // 添加到开头
        history.unshift(keyword);

        // 限制历史记录数量
        history = history.slice(0, 10);

        wx.setStorageSync('search_history', history);

        this.setData({
          searchHistory: history.slice(0, 5)
        });

        console.log('搜索历史保存成功:', keyword);
      } catch (error) {
        console.error('保存搜索历史失败:', error);
      }
    },

    // 清除搜索历史（用户退出账号时调用）
    clearSearchHistory: function() {
      try {
        wx.removeStorageSync('search_history');
        this.setData({
          searchHistory: []
        });
        console.log('搜索历史已清除');
      } catch (error) {
        console.error('清除搜索历史失败:', error);
      }
    },

    // 搜索输入事件
    onSearchInput: function(e) {
      this.setData({
        searchKeyword: e.detail.value
      });
    },

    // 搜索确认事件
    onSearchConfirm: function(e) {
      const keyword = e.detail.value.trim();
      if (keyword) {
        this.performSearch(keyword);
      }
    },

    // 搜索按钮点击事件
    onSearchTap: function() {
      const keyword = this.data.searchKeyword.trim();
      if (keyword) {
        this.performSearch(keyword);
      } else {
        wx.showToast({
          title: '请输入搜索关键词',
          icon: 'none'
        });
      }
    },

    // 执行搜索
    performSearch: async function(keyword) {
      try {
        console.log('开始搜索:', keyword);

        this.setData({
          loading: true,
          showSearchResults: true,
          error: false,
          searchResults: {
            list: [],
            total: 0,
            current: 1,
            size: 20,
            pages: 1
          }
        });

        // 保存搜索历史
        this.saveSearchHistory(keyword);

        // 调用搜索API
        const searchResult = await apiService.searchScenicsByPage({
          title: keyword,
          current: 1,
          size: 20
        });

        console.log('搜索结果:', searchResult);

        this.setData({
          loading: false,
          searchResults: searchResult
        });

        if (searchResult.list.length === 0) {
          wx.showToast({
            title: '暂无搜索结果',
            icon: 'none'
          });
        }

      } catch (error) {
        console.error('搜索失败:', error);
        this.setData({
          loading: false,
          error: true,
          errorMessage: error.message || '搜索失败，请重试'
        });

        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    },

    // 加载更多搜索结果
    loadMoreResults: async function() {
      try {
        const { searchResults, searchKeyword } = this.data;

        if (searchResults.current >= searchResults.pages) {
          return;
        }

        console.log('加载更多搜索结果');

        this.setData({
          loading: true
        });

        const nextPage = searchResults.current + 1;
        const moreResult = await apiService.searchScenicsByPage({
          title: searchKeyword,
          current: nextPage,
          size: 20
        });

        console.log('更多搜索结果:', moreResult);

        // 合并结果
        const updatedResults = {
          ...moreResult,
          list: [...searchResults.list, ...moreResult.list]
        };

        this.setData({
          loading: false,
          searchResults: updatedResults
        });

      } catch (error) {
        console.error('加载更多失败:', error);
        this.setData({
          loading: false
        });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
    },

    // 搜索历史点击事件
    onHistoryTap: function(e) {
      const keyword = e.currentTarget.dataset.keyword;
      if (keyword) {
        this.setData({
          searchKeyword: keyword
        });
        this.performSearch(keyword);
      }
    },

    // 推荐搜索点击事件
    onRecommendTap: function(e) {
      const keyword = e.currentTarget.dataset.keyword;
      if (keyword) {
        this.setData({
          searchKeyword: keyword
        });
        this.performSearch(keyword);
      }
    },

    // 景区点击事件
    onScenicTap: function(e) {
      const scenic = e.currentTarget.dataset.scenic;
      if (scenic && scenic.id) {
        console.log('跳转到景区详情页:', scenic);
        wx.navigateTo({
          url: `/pages/lanhu_dulijingqu/component?scenicId=${scenic.scenicId}&title=${encodeURIComponent(scenic.title || '')}`
        });
      }
    },

    // 返回首页或上一页
    onBackTap: function() {
      if (this.data.showSearchResults) {
        // 如果正在显示搜索结果，返回搜索页面
        this.setData({
          showSearchResults: false,
          searchKeyword: '',
          searchResults: {
            list: [],
            total: 0,
            current: 1,
            size: 20,
            pages: 1
          }
        });
      } else {
        // 否则返回上一页
        wx.navigateBack();
      }
    },

    // 清空搜索
    onClearSearch: function() {
      this.setData({
        searchKeyword: '',
        showSearchResults: false,
        searchResults: {
          list: [],
          total: 0,
          current: 1,
          size: 20,
          pages: 1
        }
      });
    }
  }
});
