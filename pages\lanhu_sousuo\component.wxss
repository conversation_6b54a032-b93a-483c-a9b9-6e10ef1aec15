.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.section_1 {
  background-color: rgba(255,255,255,1.000000);
  height: auto;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.box_1 {
  width: 370rpx;
  height: 46rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 0 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 12rpx;
}
.text_1 {
  width: 64rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.box_2 {
  width: 686rpx;
  height: 80rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 16rpx 34rpx;
}
.box_3 {
  background-color: rgba(247,248,250,1.000000);
  border-radius: 8rpx;
  width: 598rpx;
  height: 80rpx;
  display: flex;
  flex-direction: row;
}
.image-text_1 {
  width: 296rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 22rpx 0 0 30rpx;
}
.block_1 {
  background-color: #fff;
  width: 28rpx;
  height: 28rpx;
  display: flex;
  flex-direction: column;
  padding-top: 7rpx;
}
.text-group_1 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.500000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.text_2 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 20rpx;
}
.section_2 {
  width: 750rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_1 {
  width: 112rpx;
  height: 40rpx;
  display: flex;
  flex-direction: row;
  margin: 30rpx 0 0 30rpx;
}
.text_3 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.group_1 {
  width: 506rpx;
  height: 70rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 32rpx 0 0 30rpx;
}
.text-wrapper_2 {
  background-color: rgba(229,230,235,1.000000);
  border-radius: 80rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 168rpx;
}
.text_4 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 56rpx;
}
.text-wrapper_3 {
  background-color: rgba(229,230,235,1.000000);
  border-radius: 80rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 308rpx;
}
.text_5 {
  width: 196rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 56rpx;
}
.text-wrapper_4 {
  width: 112rpx;
  height: 40rpx;
  display: flex;
  flex-direction: row;
  margin: 56rpx 0 0 30rpx;
}
.text_6 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.group_2 {
  width: 650rpx;
  height: 70rpx;
  flex-direction: row;
  display: flex;
  margin: 30rpx 0 0 30rpx;
}
.text-wrapper_5 {
  background-color: rgba(229,230,235,1.000000);
  border-radius: 80rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 168rpx;
}
.text_7 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 56rpx;
}
.text-wrapper_6 {
  background-color: rgba(229,230,235,1.000000);
  border-radius: 80rpx;
  height: 70rpx;
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
  width: 168rpx;
}
.text_8 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 56rpx;
}
.text-wrapper_7 {
  background-color: rgba(229,230,235,1.000000);
  border-radius: 80rpx;
  height: 70rpx;
  margin-left: 32rpx;
  display: flex;
  flex-direction: column;
  width: 252rpx;
}
.text_9 {
  width: 140rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 56rpx;
}
.group_3 {
  width: 366rpx;
  height: 70rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text-wrapper_8 {
  background-color: rgba(229,230,235,1.000000);
  border-radius: 80rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 168rpx;
}
.text_10 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 56rpx;
}
.text-wrapper_9 {
  background-color: rgba(229,230,235,1.000000);
  border-radius: 80rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 168rpx;
}
.text_11 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 56rpx;
}
.group_4 {
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: row;
  margin: 860rpx 0 26rpx 232rpx;
}
.group_5 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
}