<view class="page">
  <view class="section_1">
    <view class="box_2">
      <view class="box_3">
        <view class="image-text_1">
          <image class="block_1" src="/images/Vector1.png"></image>
          <input lines="1" class="text-group_1" placeholder="搜索景区/博物馆/讲师" value="{{searchKeyword}}" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearchConfirm"></input>
        </view>
      </view>
      <text lines="1" class="text_2" bindtap="onSearchTap">搜索</text>
    </view>
  </view>

  <!-- 搜索结果区域 -->
  <view class="search-results" wx:if="{{showSearchResults}}">
    <view class="results-header">
      <text class="results-title">搜索结果</text>
      <text class="results-count">共{{searchResults.total}}个结果</text>
    </view>
    <view class="results-list">
      <view class="result-item" wx:for="{{searchResults.list}}" wx:key="id" bindtap="onScenicTap" data-scenic="{{item}}">
        <image class="result-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="result-content">
          <text class="result-title">{{item.title}}</text>
          <text class="result-subtitle">{{item.subtitle}}</text>
          <view class="result-info">
            <text class="result-address">{{item.address}}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{searchResults.list.length > 0 && searchResults.current < searchResults.pages}}" bindtap="loadMoreResults">
      <text class="load-more-text">加载更多</text>
    </view>
    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{searchResults.list.length > 0 && searchResults.current >= searchResults.pages}}">
      <text class="no-more-text">没有更多数据了</text>
    </view>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{searchResults.list.length === 0 && !loading}}">
      <text class="empty-text">暂无搜索结果</text>
    </view>
  </view>

  <!-- 搜索历史和推荐区域 -->
  <view class="section_2" wx:if="{{!showSearchResults}}">
    <view class="text-wrapper_1" wx:if="{{searchHistory.length > 0}}">
      <text lines="1" class="text_3">搜索历史</text>
    </view>
    <view class="group_1" wx:if="{{searchHistory.length > 0}}">
      <view class="text-wrapper_2" wx:for="{{searchHistory}}" wx:key="index" bindtap="onHistoryTap" data-keyword="{{item}}">
        <text lines="1" class="text_4">{{item}}</text>
      </view>
    </view>
    <view class="text-wrapper_4">
      <text lines="1" class="text_6">推荐搜索</text>
    </view>
    <view class="group_2">
      <view class="text-wrapper_5" bindtap="onRecommendTap" data-keyword="北京">
        <text lines="1" class="text_7">北京</text>
      </view>
      <view class="text-wrapper_6" bindtap="onRecommendTap" data-keyword="西安">
        <text lines="1" class="text_8">西安</text>
      </view>
      <view class="text-wrapper_7" bindtap="onRecommendTap" data-keyword="伊斯坦布尔">
        <text lines="1" class="text_9">伊斯坦布尔</text>
      </view>
    </view>
    <view class="group_3">
      <view class="text-wrapper_8" bindtap="onRecommendTap" data-keyword="成都">
        <text lines="1" class="text_10">成都</text>
      </view>
      <view class="text-wrapper_9" bindtap="onRecommendTap" data-keyword="重庆">
        <text lines="1" class="text_11">重庆</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text class="loading-text">搜索中...</text>
  </view>
</view>